<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>书展审核管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 状态标签样式 */
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-published { background: #d1fae5; color: #065f46; }
        .status-rejected { background: #fee2e2; color: #991b1b; }
        .status-cancelled { background: #f1f5f9; color: #475569; }
        .status-ended { background: #dbeafe; color: #1d4ed8; }

        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .btn-secondary:hover {
            background: #e2e8f0;
            color: #334155;
        }

        /* 卡片样式 */
        .card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(226, 232, 240, 0.6);
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 表格优化样式 */
        .table-fixed {
            table-layout: fixed;
        }

        /* 文本截断样式 */
        .truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 多行文本截断 */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* 表格行高度优化 */
        tbody tr {
            min-height: 80px;
        }

        /* 状态标签居中优化 */
        .status-badge {
            display: inline-block;
            min-width: 60px;
            text-align: center;
            white-space: nowrap;
        }

        /* 时间显示优化 */
        .time-info {
            font-size: 11px;
            line-height: 1.3;
        }

        /* 操作按钮列优化 */
        .action-buttons {
            min-width: 120px;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div class="min-h-screen p-6" x-data="exhibitionReviewManager()">


        <!-- 状态筛选标签页 -->
        <div class="card mb-6" x-show="hasPermission">
            <div class="flex border-b border-slate-200">
                <button @click="setStatusFilter('all')"
                        :class="currentStatusFilter === 'all' ? 'border-blue-500 text-blue-600 bg-blue-50' : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'"
                        class="px-6 py-4 border-b-2 font-medium text-sm transition-colors">
                    全部 <span x-show="statusCounts.all > 0" class="ml-2 bg-slate-100 text-slate-600 px-2 py-1 rounded-full text-xs" x-text="statusCounts.all"></span>
                </button>
                <button @click="setStatusFilter('pending_review')"
                        :class="currentStatusFilter === 'pending_review' ? 'border-blue-500 text-blue-600 bg-blue-50' : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'"
                        class="px-6 py-4 border-b-2 font-medium text-sm transition-colors">
                    待审核 <span x-show="statusCounts.pending_review > 0" class="ml-2 bg-yellow-100 text-yellow-600 px-2 py-1 rounded-full text-xs" x-text="statusCounts.pending_review"></span>
                </button>
                <button @click="setStatusFilter('approved')"
                        :class="currentStatusFilter === 'approved' ? 'border-blue-500 text-blue-600 bg-blue-50' : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'"
                        class="px-6 py-4 border-b-2 font-medium text-sm transition-colors">
                    已通过 <span x-show="statusCounts.approved > 0" class="ml-2 bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs" x-text="statusCounts.approved"></span>
                </button>
                <button @click="setStatusFilter('rejected')"
                        :class="currentStatusFilter === 'rejected' ? 'border-blue-500 text-blue-600 bg-blue-50' : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'"
                        class="px-6 py-4 border-b-2 font-medium text-sm transition-colors">
                    已拒绝 <span x-show="statusCounts.rejected > 0" class="ml-2 bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs" x-text="statusCounts.rejected"></span>
                </button>
            </div>
        </div>

        <!-- 筛选和搜索 -->
        <div class="card p-6 mb-6" x-show="hasPermission">
            <div class="flex flex-wrap gap-4 items-center">
                <div class="flex-1 min-w-64">
                    <div class="relative">
                        <input type="text"
                               x-model="searchKeyword"
                               placeholder="搜索书展标题..."
                               @keyup.enter="searchExhibitions"
                               class="w-full pl-12 pr-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                        <i class="fas fa-search absolute left-4 top-4 text-slate-400"></i>
                    </div>
                </div>
                <button @click="searchExhibitions" class="btn-primary">
                    <i class="fas fa-search mr-2"></i>搜索
                </button>
                <button @click="resetSearch" class="btn-secondary">
                    <i class="fas fa-undo mr-2"></i>重置
                </button>
            </div>
        </div>

        <!-- 权限提示 -->
        <div x-show="!hasPermission && !loading" class="card p-8 text-center mb-6">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
            </div>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">权限不足</h3>
            <p class="text-slate-500">当前用户角色无权限访问协办方审核功能，请联系管理员</p>
        </div>

        <!-- 书展列表 -->
        <div class="card" x-show="hasPermission">
            <!-- 加载状态 -->
            <div x-show="loading" class="p-12 text-center">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                    <i class="fas fa-spinner fa-spin text-2xl text-blue-600"></i>
                </div>
                <p class="text-slate-600 font-medium">加载中...</p>
            </div>

            <!-- 书展列表内容 -->
            <div x-show="!loading && exhibitions.length > 0" class="overflow-hidden">
                <div class="overflow-x-auto custom-scrollbar">
                    <table class="w-full table-fixed">
                        <thead class="bg-gradient-to-r from-slate-50 to-blue-50 border-b border-slate-200">
                            <tr>
                                <th class="w-1/3 px-4 py-4 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider">书展信息</th>
                                <th class="w-1/6 px-4 py-4 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider">学校</th>
                                <th class="w-1/6 px-4 py-4 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider">发起人</th>
                                <th class="w-1/6 px-4 py-4 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider">时间</th>
                                <th class="w-20 px-4 py-4 text-center text-xs font-semibold text-slate-700 uppercase tracking-wider">状态</th>
                                <th class="w-32 px-4 py-4 text-center text-xs font-semibold text-slate-700 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-slate-100">
                            <template x-for="exhibition in exhibitions" :key="exhibition.id">
                                <tr class="hover:bg-slate-50/50 transition-colors">
                                    <!-- 书展信息 -->
                                    <td class="px-4 py-4">
                                        <div class="flex items-start space-x-3">
                                            <img x-show="exhibition.logo_url"
                                                 :src="exhibition.logo_url"
                                                 alt="Logo"
                                                 class="w-10 h-10 rounded-lg object-cover shadow-sm flex-shrink-0 mt-1">
                                            <div class="min-w-0 flex-1">
                                                <div class="text-sm font-semibold text-slate-900 leading-5 mb-1"
                                                     x-text="exhibition.title"
                                                     style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;"></div>
                                                <div class="text-xs text-slate-500 flex items-center">
                                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                                    <span x-text="exhibition.location" class="truncate"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <!-- 学校 -->
                                    <td class="px-4 py-4">
                                        <div class="text-sm font-medium text-slate-900 leading-5" x-text="exhibition.school_name"></div>
                                    </td>
                                    <!-- 发起人 -->
                                    <td class="px-4 py-4">
                                        <div class="text-sm font-medium text-slate-900 truncate" x-text="exhibition.initiator_name"></div>
                                        <div class="text-xs text-slate-500 mt-1">
                                            <div class="truncate" x-text="exhibition.contact_name"></div>
                                            <div class="truncate" x-text="exhibition.contact_phone"></div>
                                        </div>
                                    </td>
                                    <!-- 时间 -->
                                    <td class="px-4 py-4">
                                        <div class="text-xs text-slate-600 space-y-1">
                                            <div class="flex items-center">
                                                <span class="text-slate-400 w-8">开始</span>
                                                <span x-text="exhibition.start_time"></span>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="text-slate-400 w-8">结束</span>
                                                <span x-text="exhibition.end_time"></span>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="text-slate-400 w-8">截止</span>
                                                <span x-text="exhibition.registration_deadline"></span>
                                            </div>
                                        </div>
                                    </td>
                                    <!-- 状态 -->
                                    <td class="px-4 py-4 text-center">
                                        <span class="status-badge inline-block"
                                              :class="getStatusClass(exhibition.status)"
                                              x-text="getStatusText(exhibition.status)"></span>
                                    </td>
                                    <!-- 操作 -->
                                    <td class="px-4 py-4">
                                        <div class="flex flex-col gap-1" x-show="hasPermission">
                                            <button @click="viewDetail(exhibition.id)"
                                                    class="btn-secondary text-xs px-2 py-1 w-full">
                                                <i class="fas fa-eye mr-1"></i>详情
                                            </button>
                                            <button @click="openReviewModal(exhibition)"
                                                    x-show="exhibition.status === 'pending_review'"
                                                    class="btn-primary text-xs px-2 py-1 w-full">
                                                <i class="fas fa-gavel mr-1"></i>审核
                                            </button>
                                            <button @click="viewHistory(exhibition.id, $event)"
                                                    class="btn-secondary text-xs px-2 py-1 w-full">
                                                <i class="fas fa-history mr-1"></i>历史
                                            </button>
                                            <button @click="window.openUnregisteredModal(exhibition)"
                                                    x-show="exhibition.status === 'published'"
                                                    class="btn-secondary text-xs px-2 py-1 w-full">
                                                <i class="fas fa-building mr-1"></i>未参展
                                            </button>
                                        </div>
                                        <div x-show="!hasPermission" class="text-xs text-slate-500 text-center">
                                            无权限操作
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div x-show="totalPages > 1" class="px-6 py-4 border-t border-slate-200 bg-gradient-to-r from-slate-50 to-blue-50">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-slate-700">
                            显示第 <span class="font-medium" x-text="(currentPage - 1) * pageSize + 1"></span> 到
                            <span class="font-medium" x-text="Math.min(currentPage * pageSize, totalCount)"></span> 条，
                            共 <span class="font-medium" x-text="totalCount"></span> 条记录
                        </div>
                        <div class="flex gap-1">
                            <button x-show="currentPage > 1"
                                    @click="changePage(currentPage - 1)"
                                    class="px-3 py-2 text-sm border border-slate-300 rounded-lg hover:bg-white transition-colors">
                                上一页
                            </button>
                            <template x-for="page in getPageNumbers()" :key="page">
                                <button @click="changePage(page)"
                                        :class="page === currentPage ? 'bg-blue-600 text-white' : 'hover:bg-white'"
                                        class="px-3 py-2 text-sm border border-slate-300 rounded-lg transition-colors"
                                        x-text="page"></button>
                            </template>
                            <button x-show="currentPage < totalPages"
                                    @click="changePage(currentPage + 1)"
                                    class="px-3 py-2 text-sm border border-slate-300 rounded-lg hover:bg-white transition-colors">
                                下一页
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <div x-show="!loading && exhibitions.length === 0" class="p-12 text-center">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-slate-100 rounded-full mb-4">
                    <i class="fas fa-calendar-times text-2xl text-slate-400"></i>
                </div>
                <h3 class="text-lg font-semibold text-slate-900 mb-2">暂无待审核书展</h3>
                <p class="text-slate-500">当前没有需要审核的书展活动</p>
            </div>
        </div>

        <!-- 审核模态框 -->
    <div x-show="showReviewModal"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-50 overflow-y-auto"
         @click.self="closeReviewModal()">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-lg w-full"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 scale-95"
                 x-transition:enter-end="opacity-100 scale-100"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100 scale-100"
                 x-transition:leave-end="opacity-0 scale-95">

                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-2xl">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-gavel text-blue-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-slate-800">审核书展</h3>
                            <p class="text-sm text-slate-500">请仔细审核后做出决定</p>
                        </div>
                    </div>
                    <button @click="closeReviewModal()"
                            class="w-8 h-8 bg-white hover:bg-slate-50 text-slate-600 rounded-lg flex items-center justify-center transition-colors shadow-sm">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>

                <!-- 模态框内容 -->
                <div class="p-6">
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-slate-700 mb-2">书展标题</label>
                        <div class="p-3 bg-slate-50 rounded-xl">
                            <p class="font-medium text-slate-900" x-text="currentExhibition ? currentExhibition.title : '加载中...'"></p>
                        </div>
                    </div>

                    <!-- 联系人选择区域 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-slate-700 mb-2">
                            指定联系人 <span class="text-red-500">*</span>
                        </label>
                        <div class="border border-slate-300 rounded-xl bg-white overflow-hidden">
                            <!-- 已选联系人列表 -->
                            <div x-show="selectedContacts.length > 0">
                                <div class="bg-slate-50 px-4 py-2 border-b border-slate-200">
                                    <span class="text-sm font-medium text-slate-700">已选择联系人</span>
                                    <span class="ml-2 text-xs text-slate-500" x-text="'(' + selectedContacts.length + '人)'"></span>
                                </div>
                                <div class="max-h-32 overflow-y-auto">
                                    <template x-for="(contact, index) in selectedContacts" :key="contact.id">
                                        <div class="flex items-center justify-between px-4 py-3 hover:bg-slate-50 transition-colors"
                                             :class="index < selectedContacts.length - 1 ? 'border-b border-slate-100' : ''">
                                            <div class="flex items-center space-x-3 flex-1">
                                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                    <i class="fas fa-user text-blue-600 text-sm"></i>
                                                </div>
                                                <div class="flex-1">
                                                    <div class="font-medium text-slate-900" x-text="contact.name"></div>
                                                    <div class="text-sm text-slate-500">
                                                        <span x-text="contact.phone"></span>
                                                        <span class="mx-1">•</span>
                                                        <span x-text="contact.company_name"></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-3">
                                                <!-- 保密选项 -->
                                                <label class="flex items-center text-xs text-slate-600 cursor-pointer">
                                                    <input type="checkbox"
                                                           :checked="contact.is_confidential || false"
                                                           @change="toggleSelectedContactPrivacy(contact.id)"
                                                           class="w-3 h-3 text-red-600 border-slate-300 rounded focus:ring-red-500 mr-1">
                                                    <span class="text-red-600">
                                                        <i class="fas fa-eye-slash mr-1"></i>保密
                                                    </span>
                                                </label>
                                                <!-- 删除按钮 -->
                                                <button @click="removeSelectedContact(contact.id)"
                                                        class="w-6 h-6 text-slate-400 hover:text-red-500 hover:bg-red-50 rounded-full flex items-center justify-center transition-colors"
                                                        title="移除联系人">
                                                    <i class="fas fa-times text-xs"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>

                            <!-- 空状态提示 -->
                            <div x-show="selectedContacts.length === 0" class="px-4 py-8 text-center text-slate-500">
                                <div class="flex flex-col items-center space-y-2">
                                    <i class="fas fa-user-plus text-2xl text-slate-300"></i>
                                    <span class="text-sm">尚未选择联系人</span>
                                    <span class="text-xs text-slate-400">审核通过时必须指定联系人</span>
                                </div>
                            </div>

                            <!-- 选择联系人按钮 -->
                            <div class="p-4 bg-slate-50 border-t border-slate-200">
                                <button @click="openContactModal()"
                                        type="button"
                                        class="w-full py-2 px-4 border-2 border-dashed border-slate-300 rounded-lg text-slate-600 hover:border-blue-400 hover:text-blue-600 transition-colors">
                                    <i class="fas fa-plus mr-2"></i>
                                    <span x-text="selectedContacts.length > 0 ? '添加更多联系人' : '选择联系人'"></span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium text-slate-700 mb-2">审核意见</label>
                        <textarea x-model="reviewComment"
                                  rows="4"
                                  placeholder="请输入审核意见（可选）"
                                  class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"></textarea>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex gap-3">
                        <button @click="submitReview('approve')"
                                :disabled="submitting"
                                class="flex-1 btn-success flex items-center justify-center">
                            <i class="fas fa-check mr-2" x-show="!submitting"></i>
                            <i class="fas fa-spinner fa-spin mr-2" x-show="submitting"></i>
                            <span x-text="submitting ? '处理中...' : '审核通过'"></span>
                        </button>
                        <button @click="submitReview('reject')"
                                :disabled="submitting"
                                class="flex-1 btn-danger flex items-center justify-center">
                            <i class="fas fa-times mr-2" x-show="!submitting"></i>
                            <i class="fas fa-spinner fa-spin mr-2" x-show="submitting"></i>
                            <span x-text="submitting ? '处理中...' : '审核拒绝'"></span>
                        </button>
                        <button @click="closeReviewModal()"
                                :disabled="submitting"
                                class="btn-secondary px-6">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 背景遮罩 -->
        <div class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm -z-10"></div>
        </div>
    </div>

    <!-- 联系人管理模态框 -->
    <div id="contactModal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 9999; background-color: rgba(0, 0, 0, 0.5);">
        <div style="display: flex; align-items: center; justify-content: center; min-height: 100vh; padding: 1rem;">
            <div class="bg-white rounded-2xl shadow-2xl overflow-hidden" style="width: 800px; max-height: 80vh;">

                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200 bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-2xl">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-users text-green-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-slate-800">联系人管理</h3>
                            <p class="text-sm text-slate-500">管理协办方联系人信息</p>
                        </div>
                    </div>
                    <button onclick="closeContactModal()"
                            class="w-8 h-8 bg-white hover:bg-slate-50 text-slate-600 rounded-lg flex items-center justify-center transition-colors shadow-sm">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>

                <!-- 搜索区域 -->
                <div class="p-6 border-b border-slate-200">
                    <div class="flex gap-3">
                        <div class="flex-1">
                            <input id="contactSearchInput"
                                   type="text"
                                   placeholder="搜索联系人姓名..."
                                   oninput="searchContacts()"
                                   class="w-full px-4 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        </div>
                    </div>
                </div>

                <!-- 联系人表格 -->
                <div class="flex-1 overflow-y-auto" style="max-height: 400px;">
                    <!-- 加载状态 -->
                    <div id="contactsLoading" class="p-8 text-center">
                        <div class="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-3">
                            <i class="fas fa-spinner fa-spin text-green-600"></i>
                        </div>
                        <p class="text-slate-600">加载联系人中...</p>
                    </div>

                    <!-- 联系人表格内容 -->
                    <div id="contactsContent" style="display: none;" class="p-6">
                        <!-- 表格容器，固定高度避免尺寸变化 -->
                        <div class="overflow-x-auto" style="min-height: 300px; max-height: 400px; overflow-y: auto;">
                            <table class="w-full border-collapse">
                                <thead>
                                    <tr class="bg-slate-50">
                                        <th class="border border-slate-200 px-4 py-3 text-center text-sm font-medium text-slate-700 w-16">选择</th>
                                        <th class="border border-slate-200 px-4 py-3 text-left text-sm font-medium text-slate-700">姓名</th>
                                        <th class="border border-slate-200 px-4 py-3 text-left text-sm font-medium text-slate-700">手机号</th>
                                        <th class="border border-slate-200 px-4 py-3 text-left text-sm font-medium text-slate-700">所属单位</th>
                                        <th class="border border-slate-200 px-4 py-3 text-center text-sm font-medium text-slate-700 w-24">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="contactsTableBody">
                                    <!-- 动态内容将在这里插入 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 添加更多联系人按钮 -->
                        <div class="flex justify-end mt-4">
                            <button type="button" onclick="addNewContact()"
                                    class="bg-green-500 hover:bg-green-600 text-white font-medium h-10 px-4 rounded-lg flex items-center space-x-2 text-sm transition-colors">
                                <i class="fas fa-plus"></i>
                                <span>添加更多联系人</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 底部按钮 -->
                <div class="p-6 border-t border-slate-200 bg-slate-50 rounded-b-2xl">
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-slate-600">
                            已选择 <span id="selectedContactsCount" class="font-medium text-slate-900">0</span> 个联系人
                        </div>
                        <div class="flex gap-3">
                            <button onclick="closeContactModal()"
                                    class="px-4 py-2 text-slate-600 border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors">
                                取消
                            </button>
                            <button onclick="confirmContactSelection()"
                                    class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                确定
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <!-- 未参展单位模态框（移至根层，避免受父容器display:none影响） -->
        <div id="unregisteredModal" style="display: none; z-index: 99999;" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[80vh] flex flex-col">
                <!-- 头部 -->
                <div class="flex items-center justify-between p-5 border-b border-slate-200 bg-gradient-to-r from-indigo-50 to-blue-50 rounded-t-2xl">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-building text-indigo-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-slate-800">未参展单位</h3>
                            <p id="unregisteredModalSubTitle" class="text-xs text-slate-500"></p>
                        </div>
                    </div>
                    <button onclick="window.closeUnregisteredModal()" class="w-8 h-8 bg-white hover:bg-slate-50 text-slate-600 rounded-lg flex items-center justify-center transition-colors shadow-sm">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>

                <!-- 筛选栏 -->
                <div class="p-4 border-b border-slate-200">
                    <div class="flex items-center gap-3">
                        <div class="flex rounded-lg overflow-hidden border border-slate-300">
                            <button id="tabPublisher" class="px-3 py-1.5 text-sm bg-slate-100" onclick="window.switchOrgTab('publisher')">供应商</button>
                            <button id="tabDealer" class="px-3 py-1.5 text-sm" onclick="window.switchOrgTab('dealer')">经销商</button>
                        </div>
                        <div class="relative flex-1">
                            <input id="unregisteredSearch" type="text" placeholder="搜索单位名称..." oninput="window.searchUnregistered()" class="w-full pl-9 pr-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-search absolute left-3 top-2.5 text-slate-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 内容 -->
                <div class="flex-1 overflow-y-auto p-0">
                    <div id="unregisteredLoading" class="p-10 text-center">
                        <div class="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-3">
                            <i class="fas fa-spinner fa-spin text-blue-600"></i>
                        </div>
                        <p class="text-slate-600">加载中...</p>
                    </div>
                    <div id="unregisteredContent" style="display:none;">
                        <table class="w-full">
                            <thead class="bg-slate-50 sticky top-0">
                                <tr>
                                    <th class="text-left text-sm text-slate-600 font-medium px-4 py-3">单位名称</th>
                                    <th class="w-32 text-center text-sm text-slate-600 font-medium px-4 py-3">类型</th>
                                </tr>
                            </thead>
                            <tbody id="unregisteredTBody"></tbody>
                        </table>
                        <div id="unregisteredEmpty" style="display:none;" class="py-12 text-center text-slate-500">暂无符合条件的单位</div>
                    </div>
                </div>

                <!-- 底部按钮区 -->
                <div class="p-4 border-t border-slate-200 bg-slate-50 rounded-b-2xl flex justify-end">
                    <button onclick="window.closeUnregisteredModal()" class="px-4 py-2 border border-slate-300 rounded-lg text-slate-600 hover:bg-white">关闭</button>
                </div>
            </div>
        </div>

        <script>
            let currentUnregisteredExhibitionId = null;
            let currentOrgTab = 'publisher';
            let unregisteredCache = { publisher: [], dealer: [] };

            window.openUnregisteredModal = function(exhibition){
                currentUnregisteredExhibitionId = exhibition.id;
                // 移除加载提示，根据你的要求不再弹出消息
                document.getElementById('unregisteredModal').style.display = 'flex';
                document.getElementById('unregisteredModalSubTitle').textContent = exhibition.title || '';
                // 默认切到供应商
                window.switchOrgTab('publisher', true);
            }
            window.closeUnregisteredModal = function(){
                document.getElementById('unregisteredModal').style.display = 'none';
                document.getElementById('unregisteredSearch').value = '';
                unregisteredCache = { publisher: [], dealer: [] };
            }
            window.switchOrgTab = function(tab, forceReload=false){
                currentOrgTab = tab;
                document.getElementById('tabPublisher').className = 'px-3 py-1.5 text-sm ' + (tab==='publisher'?'bg-slate-100':'');
                document.getElementById('tabDealer').className = 'px-3 py-1.5 text-sm ' + (tab==='dealer'?'bg-slate-100':'');
                if(!unregisteredCache[tab] || unregisteredCache[tab].length===0 || forceReload){
                    loadUnregistered(tab);
                }else{
                    renderUnregistered();
                }
            }
            function loadUnregistered(tab){
                document.getElementById('unregisteredLoading').style.display='block';
                document.getElementById('unregisteredContent').style.display='none';
                const search = document.getElementById('unregisteredSearch').value.trim();
                const params = new URLSearchParams({ exhibition_id: currentUnregisteredExhibitionId, org_type: tab, search });
                fetch(`/api/common/get_unregistered_companies?${params}`)
                  .then(r=>r.json())
                  .then(res=>{
                    if(res.code===0){
                        unregisteredCache[tab] = res.data.list || [];
                        renderUnregistered();
                    }else{
                        window.showMessage(res.message||'获取未参展单位失败','error');
                        unregisteredCache[tab] = [];
                        renderUnregistered();
                    }
                  })
                  .catch(err=>{
                    window.showMessage('网络错误','error');
                    unregisteredCache[tab] = [];
                    renderUnregistered();
                  })
                  .finally(()=>{
                    document.getElementById('unregisteredLoading').style.display='none';
                    document.getElementById('unregisteredContent').style.display='block';
                  });
            }
            window.searchUnregistered = function(){
                if(currentUnregisteredExhibitionId){
                    loadUnregistered(currentOrgTab);
                }
            }
            function renderUnregistered(){
                const tbody = document.getElementById('unregisteredTBody');
                const list = unregisteredCache[currentOrgTab]||[];
                tbody.innerHTML = '';
                document.getElementById('unregisteredEmpty').style.display = list.length? 'none':'block';
                list.forEach(item=>{
                    const tr = document.createElement('tr');
                    tr.className = 'hover:bg-slate-50';
                    tr.innerHTML = `
                        <td class="px-4 py-3 text-sm text-slate-800">${item.name}</td>
                        <td class="px-4 py-3 text-center">
                            <span class="inline-flex items-center justify-center px-2 py-1 text-xs rounded-full ${currentOrgTab==='publisher'?'bg-indigo-100 text-indigo-700':'bg-emerald-100 text-emerald-700'}">${currentOrgTab==='publisher'?'供应商':'经销商'}</span>
                        </td>`;
                    tbody.appendChild(tr);
                });
            }
        </script>




    <!-- 历史记录模态框 -->
    <div id="historyModal" style="display: none;" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-history text-blue-600"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-800">审核历史</h3>
                        <p id="historyModalTitle" class="text-sm text-gray-500"></p>
                    </div>
                </div>
                <button onclick="closeHistoryModal()" class="w-8 h-8 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-lg flex items-center justify-center transition-colors">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>

            <!-- 模态框内容 -->
            <div class="p-6 max-h-96 overflow-y-auto">
                <!-- 加载状态 -->
                <div id="historyLoading" class="text-center py-12">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                        <i class="fas fa-spinner fa-spin text-2xl text-blue-600"></i>
                    </div>
                    <p class="text-gray-600 font-medium">加载历史记录中...</p>
                </div>

                <!-- 历史记录列表 -->
                <div id="historyContent" style="display: none;" class="space-y-4">
                    <!-- 动态内容将在这里插入 -->
                </div>

                <!-- 空状态 -->
                <div id="historyEmpty" style="display: none;" class="text-center py-12">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                        <i class="fas fa-history text-2xl text-gray-400"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">暂无审核历史</h3>
                    <p class="text-gray-500">该书展还没有审核记录</p>
                </div>

                <!-- 错误状态 -->
                <div id="historyError" style="display: none;" class="text-center py-12">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">加载失败</h3>
                    <p id="historyErrorMessage" class="text-gray-500 mb-4"></p>
                    <button onclick="retryLoadHistory()" class="btn-primary">重试</button>
                </div>
            </div>

            <!-- 模态框底部 -->
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">


        <!-- 未参展模态（去除加载提示，优化视觉） -->
        <div id="unregisteredModal" style="display:none; z-index: 99999;" class="fixed inset-0 z-[99999]">
            <!-- 背景 -->
            <div class="absolute inset-0 bg-black/40 backdrop-blur-[1px]" onclick="window.closeUnregisteredModal()"></div>
            <!-- 弹层 -->
            <div class="absolute inset-x-0 top-[8vh] mx-auto w-[min(90vw,860px)] rounded-2xl shadow-2xl overflow-hidden">
                <div class="bg-white">
                    <!-- 头部 -->
                    <div class="flex items-center justify-between px-5 py-4 border-b border-slate-200">
                        <div class="flex items-center gap-3">
                            <div class="w-9 h-9 rounded-full bg-indigo-50 flex items-center justify-center">
                                <i class="fas fa-building text-indigo-600 text-sm"></i>
                            </div>
                            <div>
                                <div class="text-base font-semibold text-slate-800">未参展</div>
                                <div id="unregisteredModalSubTitle" class="text-xs text-slate-500"></div>
                            </div>
                        </div>
                        <button onclick="window.closeUnregisteredModal()" class="w-8 h-8 rounded-lg hover:bg-slate-100 text-slate-600 flex items-center justify-center">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>

                    <!-- 筛选 -->
                    <div class="px-5 py-3 border-b border-slate-200 bg-slate-50/60">
                        <div class="flex items-center gap-3">
                            <div class="inline-flex rounded-lg p-0.5 bg-white border border-slate-300">
                                <button id="tabPublisher" onclick="window.switchOrgTab('publisher')"
                                        class="px-3 py-1.5 text-sm rounded-md bg-slate-100">供应商</button>
                                <button id="tabDealer" onclick="window.switchOrgTab('dealer')"
                                        class="px-3 py-1.5 text-sm rounded-md">经销商</button>
                            </div>
                            <div class="relative flex-1">
                                <input id="unregisteredSearch" type="text" placeholder="搜索单位名称..."
                                       oninput="window.searchUnregistered()"
                                       class="w-full pl-9 pr-3 py-2 text-sm border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <i class="fas fa-search absolute left-3 top-2.5 text-slate-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- 内容 -->
                    <div id="unregisteredContent" class="max-h-[60vh] overflow-y-auto">
                        <table class="w-full">
                            <thead class="sticky top-0 bg-white shadow-[0_1px_0_0_#e5e7eb]">
                            <tr>
                                <th class="text-left text-xs font-medium text-slate-500 px-5 py-3">单位名称</th>
                                <th class="w-32 text-center text-xs font-medium text-slate-500 px-5 py-3">类型</th>
                            </tr>
                            </thead>
                            <tbody id="unregisteredTBody"></tbody>
                        </table>
                        <div id="unregisteredEmpty" class="hidden py-12 text-center text-slate-500">暂无符合条件的单位</div>
                    </div>

                    <!-- 底部 -->
                    <div class="px-5 py-4 border-t border-slate-200 bg-white flex justify-end">
                        <button onclick="window.closeUnregisteredModal()" class="px-4 py-2 border border-slate-300 rounded-lg text-slate-600 hover:bg-slate-50">关闭</button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            let currentUnregisteredExhibitionId = null;
            let currentOrgTab = 'publisher';
            let unregisteredCache = { publisher: [], dealer: [] };

            window.openUnregisteredModal = function(exhibition){
                currentUnregisteredExhibitionId = exhibition.id;
                document.getElementById('unregisteredModal').style.display = 'block';
                document.getElementById('unregisteredModalSubTitle').textContent = exhibition.title || '';
                window.switchOrgTab('publisher', true);
            }
            window.closeUnregisteredModal = function(){
                document.getElementById('unregisteredModal').style.display = 'none';
                document.getElementById('unregisteredSearch').value = '';
                unregisteredCache = { publisher: [], dealer: [] };
                // 清空表格，避免上次残留
                const tbody = document.getElementById('unregisteredTBody');
                if (tbody) tbody.innerHTML = '';
                const empty = document.getElementById('unregisteredEmpty');
                if (empty) empty.classList.add('hidden');
            }
            window.switchOrgTab = function(tab, forceReload=false){
                currentOrgTab = tab;
                document.getElementById('tabPublisher').className = 'px-3 py-1.5 text-sm rounded-md ' + (tab==='publisher'?'bg-slate-100':'bg-transparent');
                document.getElementById('tabDealer').className = 'px-3 py-1.5 text-sm rounded-md ' + (tab==='dealer'?'bg-slate-100':'bg-transparent');
                if(!unregisteredCache[tab] || unregisteredCache[tab].length===0 || forceReload){
                    loadUnregistered(tab);
                }else{
                    renderUnregistered();
                }
            }
            function loadUnregistered(tab){
                const search = document.getElementById('unregisteredSearch').value.trim();
                const params = new URLSearchParams({ exhibition_id: currentUnregisteredExhibitionId, org_type: tab, search });
                fetch(`/api/common/get_unregistered_companies?${params}`)
                  .then(r=>r.json())
                  .then(res=>{
                    if(res.code===0){
                        unregisteredCache[tab] = res.data.list || [];
                    }else{
                        window.showMessage && window.showMessage(res.message||'获取未参展单位失败','error');
                        unregisteredCache[tab] = [];
                    }
                    renderUnregistered();
                  })
                  .catch(()=>{
                    window.showMessage && window.showMessage('网络错误','error');
                    unregisteredCache[tab] = [];
                    renderUnregistered();
                  });
            }
            window.searchUnregistered = function(){
                if(currentUnregisteredExhibitionId){
                    loadUnregistered(currentOrgTab);
                }
            }
            function renderUnregistered(){
                const tbody = document.getElementById('unregisteredTBody');
                const list = unregisteredCache[currentOrgTab]||[];
                tbody.innerHTML = '';
                const empty = document.getElementById('unregisteredEmpty');
                empty.classList.toggle('hidden', list.length>0);
                list.forEach(item=>{
                    const tr = document.createElement('tr');
                    tr.className = 'hover:bg-slate-50';
                    tr.innerHTML = `
                        <td class="px-5 py-3 text-sm text-slate-800">${item.name}</td>
                        <td class="px-5 py-3 text-center">
                            <span class="inline-flex items-center justify-center px-2 py-1 text-xs rounded-full ${currentOrgTab==='publisher'?'bg-indigo-100 text-indigo-700':'bg-emerald-100 text-emerald-700'}">${currentOrgTab==='publisher'?'供应商':'经销商'}</span>
                        </td>`;
                    tbody.appendChild(tr);
                });
            }
        </script>

                <div class="flex justify-end">
                    <button onclick="closeHistoryModal()" class="btn-secondary px-6">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-6 right-6 space-y-2" style="z-index: 99999;"></div>

    <script>
        // 全局变量
        let currentExhibitionId = null;
        let currentExhibitionTitle = '';

        // 联系人管理全局变量
        let contacts = [];
        let filteredContacts = [];
        let globalSelectedContacts = [];
        let contactsLoading = false;

        // ========== 联系人管理全局函数 ==========

        // 打开联系人模态框
        function openContactModal() {
            console.log('打开联系人模态框');
            const modal = document.getElementById('contactModal');
            modal.style.display = 'block';
            loadContacts();
        }

        // 关闭联系人模态框
        function closeContactModal() {
            console.log('关闭联系人模态框');
            const modal = document.getElementById('contactModal');
            modal.style.display = 'none';
            document.getElementById('contactSearchInput').value = '';
            filteredContacts = [];
        }

        // 确认联系人选择
        function confirmContactSelection() {
            console.log('确认选择联系人，数量:', globalSelectedContacts.length);

            // 将选择的联系人同步到Alpine.js组件
            try {
                // 尝试多种方式获取Alpine.js组件
                let alpineComponent = null;

                // 方式1：通过Alpine.js的全局访问
                if (window.Alpine && window.Alpine.store) {
                    const dataElement = document.querySelector('[x-data*="exhibitionReviewManager"]');
                    if (dataElement && dataElement._x_dataStack) {
                        alpineComponent = dataElement._x_dataStack[0];
                    }
                }

                // 方式2：通过__x属性访问
                if (!alpineComponent) {
                    const dataElement = document.querySelector('[x-data*="exhibitionReviewManager"]');
                    if (dataElement && dataElement.__x) {
                        alpineComponent = dataElement.__x.$data;
                    }
                }

                // 方式3：直接查找包含selectedContacts的元素
                if (!alpineComponent) {
                    const elements = document.querySelectorAll('[x-data]');
                    for (let element of elements) {
                        if (element.__x && element.__x.$data && element.__x.$data.selectedContacts !== undefined) {
                            alpineComponent = element.__x.$data;
                            break;
                        }
                    }
                }

                if (alpineComponent && alpineComponent.selectedContacts !== undefined) {
                    alpineComponent.selectedContacts = [...globalSelectedContacts];
                    console.log('成功同步联系人到Alpine.js组件');
                    showMessage(`已选择 ${globalSelectedContacts.length} 个联系人`, 'success');
                } else {
                    console.warn('无法找到Alpine.js组件，使用备用方案');
                    // 备用方案：直接触发自定义事件
                    window.dispatchEvent(new CustomEvent('contactsSelected', {
                        detail: { contacts: globalSelectedContacts }
                    }));
                    showMessage(`已选择 ${globalSelectedContacts.length} 个联系人`, 'success');
                }
            } catch (error) {
                console.error('同步联系人时出错:', error);
                showMessage('联系人选择成功，但同步时出现问题', 'warning');
            }

            closeContactModal();
        }

        // 加载联系人列表
        function loadContacts() {
            contactsLoading = true;
            document.getElementById('contactsLoading').style.display = 'block';
            document.getElementById('contactsContent').style.display = 'none';
            console.log('开始加载联系人...');

            fetch('/api/common/get_co_organizer_contacts')
                .then(response => {
                    console.log('API响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(result => {
                    console.log('API响应结果:', result);
                    if (result.code === 0) {
                        contacts = result.data.map(contact => ({
                            ...contact,
                            editing: false,
                            originalData: { ...contact }
                        }));
                        filteredContacts = [...contacts];
                        console.log('联系人加载成功，数量:', contacts.length);
                        renderContactsTable();
                    } else {
                        console.error('API返回错误:', result.message);
                        showMessage(result.message || '加载联系人失败', 'error');
                    }
                })
                .catch(error => {
                    console.error('加载联系人失败:', error);
                    showMessage(`网络错误: ${error.message}`, 'error');
                })
                .finally(() => {
                    contactsLoading = false;
                    document.getElementById('contactsLoading').style.display = 'none';
                    document.getElementById('contactsContent').style.display = 'block';
                    console.log('联系人加载完成');
                });
        }

        // 渲染联系人表格
        function renderContactsTable() {
            const tbody = document.getElementById('contactsTableBody');
            tbody.innerHTML = '';

            if (filteredContacts.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="border border-slate-200 px-4 text-center text-slate-500" style="height: 250px; vertical-align: middle;">
                            <div class="flex flex-col items-center justify-center space-y-2 h-full">
                                <i class="fas fa-users text-2xl text-slate-400"></i>
                                <span>暂无联系人</span>
                                <span class="text-sm">点击下方按钮添加联系人</span>
                            </div>
                        </td>
                    </tr>
                `;
                updateSelectedContactsCount();
                return;
            }

            filteredContacts.forEach((contact, index) => {
                const isSelected = globalSelectedContacts.some(c => c.id === contact.id);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="border border-slate-200 px-4 py-3 text-center">
                        <div class="flex flex-col items-center space-y-2">
                            <input type="checkbox"
                                   ${isSelected ? 'checked' : ''}
                                   ${contact.editing ? 'disabled' : ''}
                                   onchange="toggleContactSelection(${index})"
                                   class="w-4 h-4 text-green-600 border-slate-300 rounded focus:ring-green-500">

                        </div>
                    </td>
                    <td class="border border-slate-200 px-4 py-3">
                        <input type="text"
                               value="${contact.name || ''}"
                               ${contact.editing ? '' : 'readonly'}
                               class="${contact.editing ? 'w-full px-2 py-1 border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500' : 'w-full bg-transparent border-none outline-none'}"
                               placeholder="请输入姓名"
                               onchange="updateContactField(${index}, 'name', this.value)">
                    </td>
                    <td class="border border-slate-200 px-4 py-3">
                        <input type="tel"
                               value="${contact.phone || ''}"
                               ${contact.editing ? '' : 'readonly'}
                               class="${contact.editing ? 'w-full px-2 py-1 border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500' : 'w-full bg-transparent border-none outline-none'}"
                               placeholder="请输入手机号"
                               onchange="updateContactField(${index}, 'phone', this.value)">
                    </td>
                    <td class="border border-slate-200 px-4 py-3">
                        <input type="text"
                               value="${contact.company_name || ''}"
                               ${contact.editing ? '' : 'readonly'}
                               class="${contact.editing ? 'w-full px-2 py-1 border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500' : 'w-full bg-transparent border-none outline-none'}"
                               placeholder="请输入所属单位"
                               onchange="updateContactField(${index}, 'company_name', this.value)">
                    </td>
                    <td class="border border-slate-200 px-4 py-3 text-center">
                        <div class="flex justify-center space-x-2">
                            ${!contact.editing ? `
                                <button type="button" onclick="editContact(${index})"
                                        class="text-blue-600 hover:text-blue-800 text-sm">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" onclick="deleteContact(${index})"
                                        class="text-red-600 hover:text-red-800 text-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            ` : `
                                <button type="button" onclick="saveContact(${index})"
                                        class="text-green-600 hover:text-green-800 text-sm">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button type="button" onclick="cancelContactEdit(${index})"
                                        class="text-gray-600 hover:text-gray-800 text-sm">
                                    <i class="fas fa-times"></i>
                                </button>
                            `}
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            updateSelectedContactsCount();
        }

        // 搜索联系人
        function searchContacts() {
            const searchText = document.getElementById('contactSearchInput').value.toLowerCase().trim();
            if (!searchText) {
                filteredContacts = [...contacts];
            } else {
                filteredContacts = contacts.filter(contact =>
                    contact.name.toLowerCase().includes(searchText)
                );
            }
            renderContactsTable();
        }

        // 切换联系人选择状态
        function toggleContactSelection(index) {
            const contact = filteredContacts[index];
            const selectedIndex = globalSelectedContacts.findIndex(c => c.id === contact.id);

            if (selectedIndex > -1) {
                globalSelectedContacts.splice(selectedIndex, 1);
            } else {
                // 添加联系人时初始化保密状态
                contact.is_confidential = false;
                globalSelectedContacts.push(contact);
            }

            renderContactsTable();
        }



        // 更新联系人字段
        function updateContactField(index, field, value) {
            filteredContacts[index][field] = value;
            const contactIndex = contacts.findIndex(c => c === filteredContacts[index]);
            if (contactIndex > -1) {
                contacts[contactIndex][field] = value;
            }
        }

        // 更新已选联系人数量显示
        function updateSelectedContactsCount() {
            document.getElementById('selectedContactsCount').textContent = globalSelectedContacts.length;
        }

        // 添加新联系人
        function addNewContact() {
            const newContact = {
                id: null,
                name: '',
                phone: '',
                company_name: '',
                editing: true,
                isNew: true
            };
            contacts.push(newContact);
            filteredContacts = [...contacts];
            renderContactsTable();
        }

        // 编辑联系人
        function editContact(index) {
            filteredContacts[index].editing = true;
            filteredContacts[index].originalData = { ...filteredContacts[index] };
            const contactIndex = contacts.findIndex(c => c === filteredContacts[index]);
            if (contactIndex > -1) {
                contacts[contactIndex] = filteredContacts[index];
            }
            renderContactsTable();
        }

        // 取消编辑联系人
        function cancelContactEdit(index) {
            const contact = filteredContacts[index];
            const contactIndex = contacts.findIndex(c => c === contact);

            if (contact.isNew) {
                contacts.splice(contactIndex, 1);
            } else {
                Object.assign(contacts[contactIndex], contact.originalData);
                contacts[contactIndex].editing = false;
            }
            filteredContacts = [...contacts];
            renderContactsTable();
        }

        // 保存联系人
        function saveContact(index) {
            const contact = filteredContacts[index];
            const contactIndex = contacts.findIndex(c => c === contact);

            // 验证必填字段
            if (!contact.name || !contact.phone || !contact.company_name) {
                showMessage('请填写完整的联系人信息', 'warning');
                return;
            }

            const url = contact.isNew ? '/api/common/add_co_organizer_contact' : '/api/common/edit_co_organizer_contact';
            const requestData = {
                name: contact.name,
                phone: contact.phone,
                company_name: contact.company_name
            };

            if (!contact.isNew) {
                requestData.id = contact.id;
            }

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(result => {
                if (result.code === 0) {
                    if (contact.isNew) {
                        contact.id = result.data.id;
                        contact.isNew = false;
                    }
                    contact.editing = false;
                    contacts[contactIndex] = contact;
                    filteredContacts = [...contacts];
                    renderContactsTable();
                    showMessage('联系人保存成功', 'success');
                } else {
                    showMessage(result.message || '保存失败', 'error');
                }
            })
            .catch(error => {
                console.error('保存联系人失败:', error);
                showMessage('网络错误，请稍后重试', 'error');
            });
        }

        // 删除联系人
        function deleteContact(index) {
            const contact = filteredContacts[index];
            const contactIndex = contacts.findIndex(c => c === contact);

            if (contact.isNew) {
                contacts.splice(contactIndex, 1);
                filteredContacts = [...contacts];
                renderContactsTable();
                return;
            }

            if (!confirm('确定要删除这个联系人吗？')) {
                return;
            }

            fetch('/api/common/delete_co_organizer_contact', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ id: contact.id })
            })
            .then(response => response.json())
            .then(result => {
                if (result.code === 0) {
                    contacts.splice(contactIndex, 1);
                    filteredContacts = [...contacts];
                    // 如果删除的联系人在已选列表中，也要移除
                    globalSelectedContacts = globalSelectedContacts.filter(c => c.id !== contact.id);
                    renderContactsTable();
                    showMessage(result.message, 'success');
                } else {
                    showMessage(result.message || '删除失败', 'error');
                }
            })
            .catch(error => {
                console.error('删除联系人失败:', error);
                showMessage('网络错误，请稍后重试', 'error');
            });
        }

        // 打开历史记录模态框
        function openHistoryModal(exhibitionId, exhibitions) {
            console.log('Opening history modal for exhibition:', exhibitionId);

            if (!exhibitionId) {
                showMessage('无效的书展ID', 'error');
                return;
            }

            // 查找对应的书展
            const exhibition = exhibitions.find(ex => ex.id == exhibitionId);
            if (!exhibition) {
                showMessage('找不到对应的书展信息', 'error');
                return;
            }

            currentExhibitionId = exhibitionId;
            currentExhibitionTitle = exhibition.title;

            // 设置模态框标题
            document.getElementById('historyModalTitle').textContent = exhibition.title;

            // 显示模态框
            document.getElementById('historyModal').style.display = 'flex';

            // 加载历史记录
            loadHistoryRecords(exhibitionId);
        }

        // 关闭历史记录模态框
        function closeHistoryModal() {
            console.log('Closing history modal');
            document.getElementById('historyModal').style.display = 'none';
            currentExhibitionId = null;
            currentExhibitionTitle = '';

            // 重置状态
            resetHistoryModalState();
        }

        // 重置模态框状态
        function resetHistoryModalState() {
            document.getElementById('historyLoading').style.display = 'block';
            document.getElementById('historyContent').style.display = 'none';
            document.getElementById('historyEmpty').style.display = 'none';
            document.getElementById('historyError').style.display = 'none';
            document.getElementById('historyContent').innerHTML = '';
        }

        // 加载历史记录
        function loadHistoryRecords(exhibitionId) {
            console.log('Loading history records for exhibition:', exhibitionId);

            if (!exhibitionId) {
                showHistoryError('无效的书展ID');
                return;
            }

            // 显示加载状态
            resetHistoryModalState();

            const url = `/api/common/get_exhibition_review_history?exhibition_id=${exhibitionId}`;
            console.log('Fetching history from URL:', url);

            fetch(url)
                .then(response => {
                    console.log('History API response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(result => {
                    console.log('History API result:', result);
                    if (result.code === 0) {
                        const records = result.data || [];
                        console.log('History records loaded:', records.length);
                        displayHistoryRecords(records);
                    } else {
                        console.error('History API error:', result.message);
                        showHistoryError(result.message || '获取历史记录失败');
                    }
                })
                .catch(error => {
                    console.error('获取历史记录失败:', error);
                    showHistoryError('网络错误，请稍后重试');
                });
        }

        // 显示历史记录
        function displayHistoryRecords(records) {
            document.getElementById('historyLoading').style.display = 'none';

            if (records.length === 0) {
                document.getElementById('historyEmpty').style.display = 'block';
                return;
            }

            const contentDiv = document.getElementById('historyContent');
            contentDiv.innerHTML = '';

            records.forEach(record => {
                const recordDiv = document.createElement('div');
                recordDiv.className = 'bg-slate-50 rounded-xl p-4 border border-slate-200';

                const statusClass = getHistoryStatusClass(record.action);
                const actionText = getHistoryActionText(record.action);

                recordDiv.innerHTML = `
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-user text-blue-600 text-sm"></i>
                            </div>
                            <div>
                                <div class="font-medium text-slate-900">${record.reviewer_name || '未知'}</div>
                                <div class="text-sm text-slate-500">${record.reviewer_company || '未知'}</div>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="status-badge inline-block ${statusClass}">${actionText}</span>
                            <div class="text-xs text-slate-500 mt-1">${record.created_at || ''}</div>
                        </div>
                    </div>
                    ${(record.review_comment && ['approve', 'reject'].includes(record.action)) ? `
                        <div class="mt-3 p-3 bg-white rounded-lg border border-slate-200">
                            <div class="text-sm text-slate-600 font-medium mb-1">审核意见：</div>
                            <div class="text-sm text-slate-800">${record.review_comment}</div>
                        </div>
                    ` : ''}
                `;

                contentDiv.appendChild(recordDiv);
            });

            document.getElementById('historyContent').style.display = 'block';
        }

        // 显示历史记录错误
        function showHistoryError(message) {
            document.getElementById('historyLoading').style.display = 'none';
            document.getElementById('historyErrorMessage').textContent = message;
            document.getElementById('historyError').style.display = 'block';
        }

        // 重试加载历史记录
        function retryLoadHistory() {
            if (currentExhibitionId) {
                loadHistoryRecords(currentExhibitionId);
            }
        }

        // 获取历史记录状态样式
        function getHistoryStatusClass(action) {
            const classMap = {
                'approve': 'status-published',
                'reject': 'status-rejected',
                'submit': 'status-pending',
                'resubmit': 'status-pending'
            };
            return classMap[action] || 'status-pending';
        }

        // 获取历史记录操作文本
        function getHistoryActionText(action) {
            const textMap = {
                'approve': '审核通过',
                'reject': '审核拒绝',
                'submit': '提交审核',
                'resubmit': '重新提交'
            };
            return textMap[action] || action;
        }

        // 全局消息显示函数
        function showMessage(message, type = 'info') {
            const messageId = Date.now();
            const container = document.getElementById('messageContainer');

            const bgColor = type === 'success' ? 'bg-green-500' :
                           type === 'error' ? 'bg-red-500' :
                           type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500';

            const icon = type === 'success' ? 'fa-check-circle' :
                        type === 'error' ? 'fa-exclamation-circle' :
                        type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';

            const messageEl = document.createElement('div');
            messageEl.id = `message-${messageId}`;
            messageEl.className = `max-w-sm w-full ${bgColor} text-white rounded-xl shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0`;

            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3">
                        <i class="fas ${icon}"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium">${message}</p>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()"
                            class="flex-shrink-0 ml-3 text-white/80 hover:text-white">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;

            container.appendChild(messageEl);

            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);

            // 自动移除
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.classList.add('translate-x-full', 'opacity-0');
                    setTimeout(() => {
                        if (messageEl.parentNode) {
                            messageEl.remove();
                        }
                    }, 300);
                }
            }, 5000);
        }

        // 点击模态框背景关闭
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('historyModal');
            if (modal) {
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        closeHistoryModal();
                    }
                });
            }
        });

        function exhibitionReviewManager() {
            return {
                // 数据状态
                loading: true,
                submitting: false,
                exhibitions: [],
                searchKeyword: '',
                currentPage: 1,
                pageSize: 10,
                totalCount: 0,
                totalPages: 0,
                currentStatusFilter: 'pending_review',
                statusCounts: {
                    all: 0,
                    pending_review: 0,
                    approved: 0,
                    rejected: 0
                },

                // 审核模态框 - 确保初始为false
                showReviewModal: false,
                currentExhibition: null,
                reviewComment: '',

                // 联系人相关状态 - 移到全局变量
                selectedContacts: [],

                // 移除Alpine.js历史记录状态，改用纯JavaScript实现
                hasPermission: false,

                // 初始化完成标志
                initCompleted: false,

                // 初始化
                init() {
                    console.log('=== Alpine.js 组件初始化 ===');

                    // 确保审核模态框初始状态为关闭
                    this.showReviewModal = false;

                    console.log('初始化后状态:', {
                        showReviewModal: this.showReviewModal
                    });

                    // 添加联系人选择事件监听器（备用同步方案）
                    window.addEventListener('contactsSelected', (event) => {
                        console.log('收到联系人选择事件:', event.detail.contacts);
                        this.selectedContacts = [...event.detail.contacts];
                    });

                    // 检查用户权限
                    this.checkUserPermission();

                    // 延迟加载，确保DOM完全准备好
                    setTimeout(() => {
                        this.loadExhibitions();
                        this.initCompleted = true;
                        console.log('初始化完成');
                    }, 100);
                },

                // 检查用户权限
                checkUserPermission() {
                    fetch('/api/common/check_user_session')
                        .then(response => response.json())
                        .then(result => {
                            console.log('用户session信息:', result);
                            if (result.code === 0) {
                                this.hasPermission = result.data.has_permission;
                                if (!this.hasPermission) {
                                    this.showMessage('当前用户角色无权限访问协办方审核功能', 'error');
                                }
                            } else {
                                this.showMessage('用户未登录或权限不足', 'error');
                            }
                        })
                        .catch(error => {
                            console.error('检查用户权限失败:', error);
                            this.showMessage('检查用户权限失败', 'error');
                        });
                },

                // 加载书展列表
                loadExhibitions() {
                    if (!this.hasPermission) {
                        console.log('用户无权限，跳过加载书展列表');
                        this.loading = false;
                        return;
                    }

                    console.log('开始加载书展列表');
                    this.loading = true;

                    const params = new URLSearchParams({
                        page: this.currentPage,
                        limit: this.pageSize,
                        search: this.searchKeyword.trim(),
                        status: this.currentStatusFilter
                    });

                    console.log('请求参数:', params.toString());

                    fetch(`/api/common/get_pending_exhibitions?${params}`)
                        .then(response => response.json())
                        .then(result => {
                            console.log('API响应:', result);

                            if (result.code === 0) {
                                this.exhibitions = result.data.exhibitions || [];
                                this.totalCount = result.data.total || 0;
                                this.totalPages = Math.ceil(this.totalCount / this.pageSize);
                                this.statusCounts = result.data.status_counts || {
                                    all: 0,
                                    pending_review: 0,
                                    approved: 0,
                                    rejected: 0
                                };
                                console.log('加载成功，书展数量:', this.exhibitions.length, '状态统计:', this.statusCounts);
                            } else {
                                console.error('API返回错误:', result.message);
                                this.showMessage(result.message || '获取书展列表失败', 'error');
                                this.exhibitions = [];
                            }
                        })
                        .catch(error => {
                            console.error('加载书展列表失败:', error);
                            this.showMessage('网络错误，请稍后重试', 'error');
                            this.exhibitions = [];
                        })
                        .finally(() => {
                            this.loading = false;
                            console.log('加载完成，loading状态:', this.loading);
                        });
                },

                // 搜索书展
                searchExhibitions() {
                    this.currentPage = 1;
                    this.loadExhibitions();
                },

                // 重置搜索
                resetSearch() {
                    this.searchKeyword = '';
                    this.currentPage = 1;
                    this.loadExhibitions();
                },

                // 设置状态筛选
                setStatusFilter(status) {
                    this.currentStatusFilter = status;
                    this.currentPage = 1;
                    this.loadExhibitions();
                },

                // 切换页码
                changePage(page) {
                    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                        this.currentPage = page;
                        this.loadExhibitions();
                    }
                },

                // 获取页码数组
                getPageNumbers() {
                    const pages = [];
                    const start = Math.max(1, this.currentPage - 2);
                    const end = Math.min(this.totalPages, this.currentPage + 2);

                    for (let i = start; i <= end; i++) {
                        pages.push(i);
                    }
                    return pages;
                },

                // 打开审核模态框
                openReviewModal(exhibition) {
                    console.log('打开审核模态框，书展:', exhibition);
                    if (!exhibition) {
                        console.error('没有提供书展数据');
                        return;
                    }
                    this.currentExhibition = exhibition;
                    this.reviewComment = '';
                    this.selectedContacts = [];
                    this.showReviewModal = true;
                    console.log('模态框状态已设置为:', this.showReviewModal);
                },

                // 关闭审核模态框
                closeReviewModal() {
                    console.log('关闭审核模态框');
                    this.showReviewModal = false;
                    this.currentExhibition = null;
                    this.reviewComment = '';
                    this.selectedContacts = [];
                    console.log('模态框状态已设置为:', this.showReviewModal);
                },

                // 提交审核
                submitReview(action) {
                    console.log('提交审核，动作:', action, '书展:', this.currentExhibition);
                    if (!this.currentExhibition) {
                        console.error('没有选择书展');
                        return;
                    }

                    // 审核通过时必须选择联系人
                    if (action === 'approve' && this.selectedContacts.length === 0) {
                        this.showMessage('审核通过时必须选择联系人', 'warning');
                        return;
                    }

                    this.submitting = true;

                    const requestData = {
                        exhibition_id: this.currentExhibition.id,
                        action: action,
                        comment: this.reviewComment.trim(),
                        contact_ids: this.selectedContacts.map(contact => contact.id),
                        contact_privacy: this.selectedContacts.map(contact => ({
                            contact_id: contact.id,
                            is_confidential: contact.is_confidential || false
                        }))
                    };

                    console.log('请求数据:', requestData);

                    fetch('/api/common/review_exhibition', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(requestData)
                    })
                    .then(response => response.json())
                    .then(result => {
                        console.log('审核响应:', result);

                        if (result.code === 0) {
                            this.showMessage(result.message, 'success');
                            this.closeReviewModal();
                            this.loadExhibitions();
                        } else {
                            this.showMessage(result.message || '审核失败', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('审核失败:', error);
                        this.showMessage('网络错误，请稍后重试', 'error');
                    })
                    .finally(() => {
                        this.submitting = false;
                    });
                },

                // 查看书展详情
                viewDetail(exhibitionId) {
                    window.location.href = `/exhibition_detail?id=${exhibitionId}`;
                },

                // 查看审核历史 - 使用纯JavaScript实现
                viewHistory(exhibitionId, event) {
                    // 调用全局函数
                    window.openHistoryModal(exhibitionId, this.exhibitions);
                },

                // 获取状态样式类
                getStatusClass(status) {
                    const statusMap = {
                        'pending_review': 'status-pending',
                        'published': 'status-published',
                        'rejected': 'status-rejected',
                        'cancelled': 'status-cancelled',
                        'ended': 'status-ended'
                    };
                    return statusMap[status] || 'status-pending';
                },

                // 获取状态文本
                getStatusText(status) {
                    const statusMap = {
                        'pending_review': '待审核',
                        'published': '已发布',
                        'rejected': '已拒绝',
                        'cancelled': '已取消',
                        'ended': '已结束'
                    };
                    return statusMap[status] || status;
                },

                // 显示消息 - 使用全局函数
                showMessage(message, type = 'info') {
                    window.showMessage(message, type);
                },

                // ========== 联系人管理相关方法 - 使用全局函数 ==========

                // 打开联系人选择模态框 - 调用全局函数
                openContactModal() {
                    window.openContactModal();
                },

                // 移除已选联系人 - 保留这个方法供审核模态框使用
                removeSelectedContact(contactId) {
                    const index = this.selectedContacts.findIndex(c => c.id === contactId);
                    if (index > -1) {
                        this.selectedContacts.splice(index, 1);
                    }
                    // 同步到全局变量
                    globalSelectedContacts = globalSelectedContacts.filter(c => c.id !== contactId);
                },

                // 切换已选联系人的保密状态
                toggleSelectedContactPrivacy(contactId) {
                    const index = this.selectedContacts.findIndex(c => c.id === contactId);
                    if (index > -1) {
                        // 切换保密状态
                        this.selectedContacts[index].is_confidential = !this.selectedContacts[index].is_confidential;

                        // 同步到全局变量
                        const globalIndex = globalSelectedContacts.findIndex(c => c.id === contactId);
                        if (globalIndex > -1) {
                            globalSelectedContacts[globalIndex].is_confidential = this.selectedContacts[index].is_confidential;
                        }

                        console.log(`联系人 ${this.selectedContacts[index].name} 保密状态已设置为: ${this.selectedContacts[index].is_confidential}`);
                    }
                },


            }
        }

    </script>
    <script src="/static/js/alpine.min.js"></script>
</body>
</html>
